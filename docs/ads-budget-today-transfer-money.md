# 广告预算弹窗组件"今日已充金额"功能实现文档

## 功能概述

为广告预算弹窗组件实现"今日已充金额"字段的完整功能，该字段显示每个推广人员在当天的充值成功总金额。

## 实现方案

### 1. 数据关联关系

- **推广人员**: `backend_member` 表中的推广相关角色用户
- **广告账户**: `ads_account_sub` 表，通过 `responsible_id` 字段关联推广人员
- **充值记录**: `ads_transfer_money_record` 表，记录所有充值操作
- **预算配置**: `ads_advertising_budget` 表，配置推广人员的每日预算

### 2. 核心逻辑

#### 数据查询流程：
1. 根据推广人员ID查询其负责的所有广告账户（`ads_account_sub.responsible_id`）
2. 查询这些广告账户在今日的成功充值记录（`ads_transfer_money_record.status = 1`）
3. 按推广人员汇总今日充值总金额
4. 返回格式化的金额数据

#### 时间范围：
- 今日开始时间：当天 00:00:00
- 今日结束时间：当天 23:59:59

#### 充值状态：
- 只统计 `status = 1` 的成功充值记录
- 按平台（`platform`）进行筛选

### 3. 代码实现

#### 后端实现（auth/services/promote/AdsAdvertisingBudgetService.php）

新增方法：`getTodayTransferAmountsByUserIds($userIds, $platform)`

**功能特性：**
- 批量查询多个推广人员的今日已充金额
- 支持按平台筛选充值记录
- 包含错误处理和日志记录
- 金额保留两位小数
- 企业级数据隔离

**核心SQL逻辑：**
```sql
-- 1. 查询推广人员负责的广告账户
SELECT sub_advertiser_id, responsible_id 
FROM erp_ads_account_sub 
WHERE responsible_id IN (用户ID列表) 
AND entity_id = 当前企业ID

-- 2. 查询今日成功充值记录
SELECT target_advertiser_id, amount 
FROM erp_ads_transfer_money_record 
WHERE status = 1 
AND platform = '平台名称'
AND created_at >= 今日开始时间戳
AND created_at <= 今日结束时间戳
AND entity_id = 当前企业ID
```

#### 前端实现（manageSystem/src/components/selfComponents/self-ads-budget-modal/index.vue）

**表格列配置：**
```javascript
{
  title: "今日已充金额(元)",
  dataIndex: "today_transfer_money",
  key: "today_transfer_money",
  width: 150,
  scopedSlots: { customRender: "today_transfer_money" }
}
```

**显示模板：**
```vue
<template slot="today_transfer_money" slot-scope="text">
  {{ text > 0 ? parseFloat(text).toFixed(2) : '-' }}
</template>
```

### 4. 数据表结构

#### ads_transfer_money_record（充值记录表）
- `target_advertiser_id`: 目标广告账户ID
- `amount`: 充值金额（DECIMAL(10,2)）
- `status`: 充值状态（1=成功）
- `platform`: 充值平台（tiktok/adq）
- `created_at`: 充值时间（UNIX时间戳）
- `entity_id`: 企业ID

#### ads_account_sub（广告子账户表）
- `sub_advertiser_id`: 广告子账户ID
- `responsible_id`: 责任人ID（推广人员ID）
- `entity_id`: 企业ID

### 5. 性能优化

- 使用批量查询减少数据库访问次数
- 在内存中进行数据聚合计算
- 添加适当的数据库索引（target_advertiser_id, created_at）
- 错误处理确保系统稳定性

### 6. 测试验证

#### 测试场景：
1. 推广人员有充值记录的情况
2. 推广人员无充值记录的情况
3. 推广人员无负责账户的情况
4. 跨平台数据隔离验证
5. 企业数据隔离验证

#### 预期结果：
- 有充值记录：显示正确的金额总和（保留两位小数）
- 无充值记录：显示 "-"
- 数据按企业和平台正确隔离

### 7. 兼容性说明

- **PHP版本**: 兼容PHP 7.4.33
- **框架版本**: 基于Yii2框架
- **前端框架**: Vue2 + Ant Design Vue
- **数据库**: MySQL 5.7+

### 8. 维护说明

- 日志记录：充值金额查询错误会记录到 'ads_budget' 分类
- 错误处理：查询失败时返回0，不影响页面正常显示
- 扩展性：支持新增平台和新的统计维度

## 使用方式

1. 打开广告预算弹窗组件
2. 选择对应的平台标签（抖音/ADQ）
3. 查看表格中的"今日已充金额(元)"列
4. 金额显示格式：有数据显示具体金额（如：123.45），无数据显示"-"
