# 广告预算充值账户状态验证功能实现文档

## 功能概述

在 `TransferMoneyServiceV2.php` 的 `run()` 方法中实现账户状态验证功能，确保只有状态正常的账户和绑定在职投放人员的账户才能进行充值操作。

## 实现方案

### 1. 验证规则

#### 账户状态验证：
- 检查目标充值账户的启用状态（`ads_account_sub.status`）
- 只有状态为"启用"（`AdsAccountSubStatusEnum::ENABLED = 1`）的账户才允许充值
- 禁用（0）和备用（2）状态的账户不允许充值

#### 投放人员状态验证：
- 检查绑定到账户的投放人员状态（`backend_member.status`）
- 只有状态为"启用"（`StatusEnum::ENABLED = 1`）的投放人员才允许其管理的账户充值
- 禁用（0）状态的投放人员视为离职，其管理的账户不允许充值

### 2. 代码实现

#### 新增错误码
```php
private $error_code_account_status = 426; // 账户状态错误码
```

#### 主要方法

**checkAccountStatus() - 账户状态验证**
- 批量查询账户信息和投放人员状态
- 检查账户启用状态
- 检查投放人员在职状态
- 生成详细错误信息

### 3. 集成位置

在 `run()` 方法中的集成位置：
```php
// 参数处理和账户验证
$result = $this->dealParams($params);
$accountPlatformMap = $this->verificationAccountWithPlatforms($result);
$data['target_advertiser_ids'] = array_keys($accountPlatformMap);
$data['amount'] = $this->verificationAmount($result, $accountPlatformMap);

// 账户状态验证 - 新增功能
$this->checkAccountStatus($data['target_advertiser_ids']);

// 充值限额检查
$this->checkBudgetLimit($data['target_advertiser_ids'], $data['amount'], $accountPlatformMap);

// 定时充值判断
$this->isTimeRecharge($result);
```

### 4. 错误返回格式

当账户状态验证失败时，返回详细错误信息：

**单个账户错误示例：**
```
**************** 账户状态为禁用，非启用状态，请前往账户管理确认账户状态后再进行充值
```

**投放人员状态错误示例：**
```
**************** 绑定的投放人员(张三)状态异常或已离职，请前往账户管理确认账户状态后再进行充值
```

**多个账户错误示例：**
```
**************** 账户状态为禁用，非启用状态；**************** 绑定的投放人员(张三)状态异常或已离职，请前往账户管理确认账户状态后再进行充值
```

**错误码：426**

### 5. 技术特性

#### 批量验证支持
- 一次查询获取所有目标账户的状态信息
- 支持多账户同时验证
- 高效的数据库查询优化

#### 详细错误信息
- 明确指出具体哪个账户存在问题
- 区分账户状态问题和投放人员状态问题
- 提供投放人员姓名信息便于识别

#### 企业级数据隔离
- 基于当前企业ID进行数据查询
- 确保多企业环境下的数据安全

#### 兼容性保障
- 异常处理确保验证失败不影响系统稳定性
- 向后兼容现有充值流程
- 记录详细日志便于问题排查

### 6. 数据表依赖

#### ads_account_sub（广告子账户表）
- `sub_advertiser_id`: 广告子账户ID
- `sub_advertiser_name`: 账户名称
- `status`: 账户状态（0=禁用，1=启用，2=备用）
- `responsible_id`: 责任人ID（投放人员ID）
- `entity_id`: 企业ID

#### backend_member（后台成员表）
- `id`: 成员ID
- `username`: 用户名
- `realname`: 真实姓名
- `status`: 成员状态（0=禁用，1=启用）

### 7. 验证流程

#### 正常充值流程
1. 推广人员发起充值请求
2. 系统验证账户和金额
3. **执行账户状态验证**（新增）
4. 执行充值限额检查
5. 通过所有检查后继续原有充值流程

#### 账户状态异常场景
1. 推广人员发起充值请求
2. 系统检测到账户状态异常或投放人员离职
3. 返回错误码426和详细错误信息
4. 充值被拒绝，不执行实际充值操作

#### 无账户信息场景
1. 账户ID不存在于系统中
2. 返回"账户不存在"错误信息
3. 充值被拒绝

### 8. 状态枚举说明

#### AdsAccountSubStatusEnum（账户状态）
- `DISABLED = 0`: 禁用
- `ENABLED = 1`: 启用（允许充值）
- `STANDBY = 2`: 备用

#### StatusEnum（成员状态）
- `DISABLED = 0`: 禁用（离职）
- `ENABLED = 1`: 启用（在职，允许充值）

### 9. 监控和日志

#### 日志记录
- 账户状态检查异常：`account_status_check` 分类
- 充值被拒绝：通过异常机制记录

#### 错误追踪
- 错误码426专门用于账户状态异常
- 详细错误信息包含账户ID和具体问题
- 支持问题排查和运营处理

### 10. 使用场景

#### 账户被禁用场景
- 运营人员将问题账户设置为禁用状态
- 系统自动阻止对该账户的充值操作
- 返回明确的状态错误信息

#### 投放人员离职场景
- HR将离职员工账户设置为禁用状态
- 系统自动阻止对其管理账户的充值操作
- 避免离职员工账户产生充值风险

#### 批量账户管理场景
- 支持同时验证多个账户状态
- 一次性返回所有问题账户信息
- 提高运营处理效率

### 11. 兼容性说明

- **PHP版本**: 兼容PHP 7.4.33
- **向后兼容**: 不影响现有充值流程
- **异常处理**: 确保状态检查失败不影响系统稳定性
- **数据查询**: 优化的批量查询减少数据库压力
