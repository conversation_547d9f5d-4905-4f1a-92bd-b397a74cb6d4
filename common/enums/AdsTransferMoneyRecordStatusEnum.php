<?php

namespace common\enums;

/**
 * 广告预算充值状态
 *
 * Class AdsTransferMoneyRecordStatusEnum
 * @package common\enums
 */
class AdsTransferMoneyRecordStatusEnum extends BaseEnum
{
    const SUCCESS = 1;
    const FAILURE = 2;

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::SUCCESS => '成功',
            self::FAILURE => '失败',
        ];
    }
}