<?php

namespace common\models\backend;

use auth\models\TeacherJob;
use common\enums\AppEnum;
use common\enums\MemberTypeEnum;
use common\enums\StatusEnum;
use common\helpers\ArrayHelper;
use common\models\base\User;
use common\models\Config;
use common\models\rbac\AuthAssignment;
use common\models\rbac\AuthRole;
use services\UserService;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%backend_member}}".
 *
 * @property int $id
 * @property string $merchant_id 商户id
 * @property string $username 帐号
 * @property string $password_hash 密码
 * @property string $auth_key 授权令牌
 * @property string $password_reset_token 密码重置令牌
 * @property int $type 1:普通管理员;10超级管理员
 * @property string $realname 真实姓名
 * @property string $head_portrait 头像
 * @property int $gender 性别[0:未知;1:男;2:女]
 * @property string $qq qq
 * @property string $email 邮箱
 * @property string $birthday 生日
 * @property int $province_id 省
 * @property int $city_id 城市
 * @property int $area_id 地区
 * @property string $address 默认地址
 * @property string $mobile 手机号码
 * @property string $home_phone 家庭号码
 * @property string $position 职位
 * @property string $jobnumber 工号
 * @property string $dingtalk_robot_token 机器人token
 * @property string $dingtalk_unionid_id 用户钉钉的unionid
 * @property string $dingtalk_user_id 用户钉钉的userID
 * @property string $feishu_unionid 用户飞书的unionid
 * @property string $feishu_userid 用户飞书的userid
 * @property int $visit_count 访问次数
 * @property int $last_time 最后一次登录时间
 * @property string $last_ip 最后一次登录ip
 * @property int $current_entity_id 当前企业ID
 * @property int $current_feishu_app_id 当前飞书应用id,表：feishu_app 表ID
 * @property int $role 权限
 * @property int $status 状态[0:禁用;1启用]
 * @property string $created_at 创建时间
 * @property string $updated_at 修改时间
 */
class Member extends User
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%backend_member}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'gender', 'province_id', 'city_id', 'area_id', 'visit_count', 'last_time', 'role', 'status', 'current_entity_id','current_feishu_app_id', 'created_at', 'updated_at'], 'integer'],
            [['birthday'], 'safe'],
            [['username', 'qq', 'mobile', 'home_phone', 'realname', 'jobnumber'], 'string', 'max' => 20],
            [['password_hash', 'password_reset_token', 'head_portrait'], 'string', 'max' => 200],
            [['auth_key'], 'string', 'max' => 32],
            [['email'], 'string', 'max' => 60],
            [['address'], 'string', 'max' => 100],
            [['position'], 'string', 'max' => 500],
            [['dingtalk_robot_token', 'dingtalk_unionid_id', 'dingtalk_user_id', 'feishu_unionid', 'feishu_userid'], 'string', 'max' => 100],
            [['last_ip'], 'string', 'max' => 16],
            ['type', 'in', 'range' => MemberTypeEnum::getKeys()],
            ['status', 'in', 'range' => StatusEnum::getKeys()],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'username' => '账号',
            'password_hash' => '密码',
            'auth_key' => '授权key',
            'password_reset_token' => '密码重置',
            'type' => '类型',
            'realname' => '真实姓名',
            'head_portrait' => '头像',
            'gender' => '性别',
            'qq' => 'QQ',
            'email' => '邮箱',
            'birthday' => '生日',
            'province_id' => '省',
            'city_id' => '市',
            'area_id' => '区',
            'address' => '地址',
            'mobile' => '手机号码',
            'home_phone' => '电话号码',
            'position' => '职位',
            'jobnumber' => '工号',
            'dingtalk_robot_token' => '钉钉机器人Token',
            'dingtalk_unionid_id' => '用户钉钉的unionid',
            'dingtalk_user_id' => '用户钉钉的userID',
            'visit_count' => '访问次数',
            'last_time' => '最后一次登录时间',
            'last_ip' => '最后一次登录IP',
            'current_entity_id' => '当前企业ID',
            'current_feishu_app_id' => '当前飞书应用ID',
            'role' => '权限',
            'status' => '状态',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
        ];
    }

    /**
     * @param bool $insert
     * @return bool
     * @throws \yii\base\Exception
     */
    public function beforeSave($insert)
    {
        if ($this->isNewRecord) {
            $this->auth_key = Yii::$app->security->generateRandomString();
        }

        return parent::beforeSave($insert);
    }

    /**
     * @return bool
     */
    public function beforeDelete()
    {
        AuthAssignment::deleteAll(['user_id' => $this->id, 'app_id' => AppEnum::BACKEND_API]);
        return parent::beforeDelete();
    }

    /**
     * @return array
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
            ]
        ];
    }

    public function getDepartmentAssignment()
    {
        return $this->hasOne(\common\models\common\DepartmentAssignment::class, ['user_id' => 'id']);
    }

    /**
     * 获取用户名称
     *
     * @param $user_id
     * @return mixed|string
     */
    public static function getMemberName($user_id)
    {
        $info = Member::find()->where(['id' => $user_id])->select('username')->one();
        return $info ? $info->username : '';
    }

    /**
     * 获取用户角色
     *
     * @param $user_id
     * @return array
     */
    public static function getMemberRole($user_id)
    {
        $role_name = [];
        $type = Member::find()->select('type')->where(['id' => $user_id])->asArray()->one()['type'];
        if ($type == MemberTypeEnum::SUPER_ADMIN) {
            $role_name[] = ['id' => 0, 'name' => MemberTypeEnum::getValue($type)];
        } else {
            $assignment = AuthAssignment::find()->select('role_id')->where(['user_id' => $user_id])->column();
            foreach ($assignment as $role_id) {
                $role = AuthRole::find()->select('id,title')->where(['id' => $role_id])->one();
                if ($role) {
                    $role_name[] = [
                        'id' => $role->id,
                        'name' => $role->title,
                    ];
                }
            }
        }
        return $role_name;
    }

    /**
     * 判断是否包含角色
     *
     * @param $userId
     * @param $roles
     * @return bool
     */
    public static function hasRoles($userId, $roles)
    {
        if (!is_array($roles)) {
            $roles = [$roles];
        }
        $roleList = self::getMemberRole($userId);
        foreach ($roleList as $role) {
            if (in_array($role['name'], $roles)) return true;
        }

        return false;
    }

    /**
     * 判断是成员是否某个角色
     */
    public function isRole($roleName)
    {
        $roleNames = $this->roleNames;
        if (!is_array($roleNames) || empty($roleNames)) {
            return false;
        }

        return in_array($roleName, $roleNames);
    }

    /**
     * 是否是店长
     *
     * @return bool
     */
    public function isStoreDirector()
    {
        $role = AuthRole::getStoreDirectorRole();
        $userRoleIDs = self::getMemberRole($this->id);
        foreach ($userRoleIDs as $key => $value) {
            if ($value['id'] == $role->id) return true;
        }
        return false;
    }

    /**
     * 判断用户是否是推广人员
     *
     * @param $user_id
     * @param array $role_name
     * @return array|bool|ActiveRecord|null
     */
    public static function userIsPromotePerson($user_id, $role_name = [])
    {
        $andWhere = ['like', 'r.title', '推广'];
        if ($role_name) $andWhere = ['in', 'r.title', $role_name];

        $user = Member::find()->alias('m')->select('m.id,m.username')
            ->leftJoin(['ur' => '{{%rbac_auth_assignment}}'], 'ur.user_id = m.id')
            ->leftJoin(['r' => '{{%rbac_auth_role}}'], 'r.id = ur.role_id')
            ->where(['m.id' => $user_id, 'm.status' => StatusEnum::ENABLED])
            ->andWhere($andWhere)->one();

        if (empty($user)) return false;
        return $user;
    }

    /**
     * 判断用户是否是指定角色
     *
     * @param $user_id
     * @param string $role_name
     * @return array|bool|ActiveRecord|null
     */
    public static function userIsAppointRole($user_id, $role_name)
    {
        $user = Member::find()->alias('m')->select('m.id,m.username')
            ->cache(60)
            ->leftJoin(['ur' => '{{%rbac_auth_assignment}}'], 'ur.user_id = m.id')
            ->leftJoin(['r' => '{{%rbac_auth_role}}'], 'r.id = ur.role_id')
            ->where(['m.id' => $user_id, 'm.status' => StatusEnum::ENABLED])
            ->andWhere(['like', 'r.title', $role_name])
            ->one();

        if (empty($user)) return false;
        return true;
    }

    public function getAuthAssignments()
    {
        return $this->hasMany(AuthAssignment::class, ['user_id' => 'id']);
    }

    public function getRoleNames()
    {
        return ArrayHelper::getColumn($this->authAssignments ?: [], 'role.title');
    }

    public function getServicerRoleNames()
    {
        $roleNames = [];
        $roleIds = Config::getByName('serviceRoleIds');
        if (empty($roleIds)) {
            return [];
        }
        foreach ($this->authAssignments as $authAsig) {
            if (in_array($authAsig->role_id, $roleIds)) {
                $roleNames[] = $authAsig->role->title;
            }
        }
        return $roleNames;
    }

    /**
     * 关键词搜索相关id
     *
     * @param $keyword
     * @return array
     */
    public static function getIdsByKeyword($keyword)
    {
        return static::find()
            ->andWhere(['username' => $keyword])
            ->select('id')
            ->column();
    }

    /**
     * 获取其他企业的
     */
    public function getOtherEntityUserId($entityId)
    {
        $userId = self::find()
            ->where(['current_entity_id' => $entityId])
            ->andWhere(['realname' => $this->realname])
            ->select('id')
            ->scalar();
        return $userId ?: 0;
    }

    /**
     * 获取飞书unionid
     * 
     * @param int $userId
     * @return string
     */
    public static function getFeishuUnionId($userId)
    {
        return self::find()->select('feishu_unionid')->where(['id' => $userId])->scalar();
    }
}
