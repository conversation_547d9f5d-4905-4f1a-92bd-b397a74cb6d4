<?php

namespace auth\services\promote;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\promote\AdsAdvertisingBudget;
use common\models\rbac\AuthAssignment;
use common\models\rbac\AuthRole;
use common\models\backend\Member;
use common\models\promote\AdsTransferMoneyRecord;
use common\models\common\AdsAccountSub;
use common\enums\StatusEnum;
use common\enums\AdsTransferMoneyRecordStatusEnum;
use common\enums\AppEnum;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\services\promote\AdsAdvertisingBudgetService as CommonAdsAdvertisingBudgetService;
use Yii;

class AdsAdvertisingBudgetService extends CommonAdsAdvertisingBudgetService
{
    const PROMOTE_AUTH_ROLE_TITLE = [
        '推广专员',
        '推广经理',
        '推广总监',
        '推广主管'
    ];

    /**
     * @var AdsAdvertisingBudget
     */
    public static $modelClass = AdsAdvertisingBudget::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = Member::find()
            ->alias('m')
            ->select(['m.id as member_id', 'm.username'])
            ->leftJoin(['aa' => AuthAssignment::tableName()], 'm.id = aa.user_id')
            ->leftJoin(['ar' => AuthRole::tableName()], 'ar.id = aa.role_id')
            ->where(['ar.title' => self::PROMOTE_AUTH_ROLE_TITLE])
            ->andWhere(['m.current_entity_id' => Yii::$app->user->identity->current_entity_id])
            ->andWhere(['m.status' => StatusEnum::ENABLED])
            ->andWhere(['aa.app_id' => AppEnum::BACKEND_API])
            ->andFilterWhere(['like', 'm.username', $params['username']]);

        $query->offset($offset)->limit($limit)->groupBy('m.id')->orderBy('m.id ASC');
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
        ]);

        $query = static::getQuery($params);
        $totalCount = $query->count();
        $list = $query->asArray()->all();

        $platform = ArrayHelper::getValue($params, 'platform');
        if (empty($platform)) {
            throw new Exception('请选择平台');
        }   

        $budgets = static::$modelClass::findAll(['platform' => $platform]);
        if (empty($budgets)) {
            return [$list, $totalCount];
        }

        $userIdToBudgetId = [];
        $memberBudgetMapping = [];
        foreach ($budgets as $budget) {
            $userIdToBudgetId[$budget->promote_user_id] = $budget->id;
            ArrayHelper::setValue($memberBudgetMapping, $budget->promote_user_id.'.'.$budget->platform, $budget->budget);
        }
        // 获取今日已充金额数据
        $todayTransferAmounts = static::getTodayTransferAmountsByUserIds(
            array_column($list, 'member_id'),
            $platform
        );

        foreach ($list as $key => $item) {
            $list[$key]['budget'] = ArrayHelper::getValue($memberBudgetMapping, $item['member_id'].'.'.$platform, '');
            $list[$key]['budget_id'] = ArrayHelper::getValue($userIdToBudgetId, $item['member_id'], 0);
            // 设置今日已充金额
            $list[$key]['today_transfer_money'] = ArrayHelper::getValue($todayTransferAmounts, $item['member_id'], 0);
        }

        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            $info = new static::$modelClass();
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 批量获取推广人员今日已充金额
     *
     * @param array $userIds 推广人员ID数组
     * @param string $platform 广告平台
     * @return array 用户ID => 今日已充金额的映射数组
     */
    public static function getTodayTransferAmountsByUserIds($userIds, $platform)
    {
        if (empty($userIds) || empty($platform)) {
            return [];
        }

        try {
            // 今日开始和结束时间戳
            $todayTime = DateHelper::today();

            // 获取当前企业ID
            $entityId = Yii::$app->user->identity->current_entity_id ?? 0;
            if (empty($entityId)) {
                Yii::warning('获取今日已充金额时企业ID为空', 'ads_budget');
                return array_fill_keys($userIds, 0);
            }

            // 获取推广人员负责的广告账户ID列表
            $accountSubIds = AdsAccountSub::find()
                ->select(['sub_advertiser_id', 'responsible_id'])
                ->where(['responsible_id' => $userIds])
                ->andWhere(['entity_id' => $entityId])
                ->asArray()
                ->all();

            if (empty($accountSubIds)) {
                return array_fill_keys($userIds, 0);
            }

            // 按推广人员分组广告账户ID
            $userAccountMapping = [];
            foreach ($accountSubIds as $account) {
                if (!empty($account['sub_advertiser_id'])) {
                    $userAccountMapping[$account['responsible_id']][] = $account['sub_advertiser_id'];
                }
            }

            // 查询今日充值记录
            $transferRecords = AdsTransferMoneyRecord::find()
                ->select(['target_advertiser_id', 'amount'])
                ->where(['status' => AdsTransferMoneyRecordStatusEnum::SUCCESS]) // 只统计成功的充值
                ->andWhere(['platform' => $platform])
                ->andWhere(['>=', 'created_at', $todayTime['start']])
                ->andWhere(['<=', 'created_at', $todayTime['end']])
                ->andWhere(['entity_id' => $entityId])
                ->asArray()
                ->all();

            // 按广告账户ID统计充值金额
            $accountAmountMapping = [];
            foreach ($transferRecords as $record) {
                $accountId = $record['target_advertiser_id'];
                if (!empty($accountId)) {
                    if (!isset($accountAmountMapping[$accountId])) {
                        $accountAmountMapping[$accountId] = 0;
                    }
                    $accountAmountMapping[$accountId] = BcHelper::add($accountAmountMapping[$accountId], (float)$record['amount']);
                }
            }

            // 按推广人员汇总今日已充金额
            $result = [];
            foreach ($userIds as $userId) {
                $totalAmount = 0;
                if (isset($userAccountMapping[$userId])) {
                    foreach ($userAccountMapping[$userId] as $accountId) {
                        if (isset($accountAmountMapping[$accountId])) {
                            $totalAmount = BcHelper::add($totalAmount, $accountAmountMapping[$accountId]);
                        }
                    }
                }
                $result[$userId] = BcHelper::sprintf($totalAmount);
            }

            return $result;

        } catch (Exception $e) {
            Yii::error('获取今日已充金额失败: ' . $e->getMessage(), 'getTodayTransferAmountsByUserIds');
            return array_fill_keys($userIds, 0);
        }
    }
}
