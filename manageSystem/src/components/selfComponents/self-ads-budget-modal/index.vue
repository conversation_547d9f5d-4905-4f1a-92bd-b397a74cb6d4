<template>
  <div>
    <a-button type="primary" @click="openModal">
      广告预算
    </a-button>
    
    <self-detail-modal
      title="广告预算"
      :show-confirm="false"
      placement="center"
      v-model="visible"
      :confirmLoading="confirmLoading"
      :drawerWidth="'80%'"
      padding="0 24px"
      @ok="handleOk"
      :close="false"
    >
      <template slot="centent" slot-scope="data">
        <a-tabs :default-active-key="defaultActiveKey" @change="onTabChange">
          <a-tab-pane
            v-for="item in tabList"
            :key="item.platform"
            :tab="item.name"
          >
            <div
              class="budgetBox"
              :style="{
                height: data.height - 80 + 'px'
              }"
            >
              <a-input-search
                placeholder="请输入要查找的姓名"
                enter-button="查询"
                @search="searchQuery"
                @pressEnter="searchQuery"
                v-model="keyword"
                style="margin-bottom: 16px"
              />
              
              <a-table
                :columns="columns"
                :dataSource="tableData"
                :loading="tableLoading"
                :pagination="pagination"
                @change="handleTableChange"
                :rowKey="record => record.member_id"
                size="middle"
              >
                <template slot="budget" slot-scope="text, record">
                  <a-input-number
                    v-model="record.budget"
                    :min="0"
                    :max="99999"
                    :precision="0"
                    style="width: 120px"
                    placeholder="请输入预算"
                    @blur="onBudgetBlur(record)"
                    @focus="onBudgetFocus(record)"
                  />
                </template>
                
                <template slot="today_transfer_money" slot-scope="text">
                  {{ text > 0 ? parseFloat(text).toFixed(2) : '-' }}
                </template>
              </a-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </template>
    </self-detail-modal>
    
    <!-- 确认修改预算的小弹窗 -->
    <a-modal
      title="确认修改预算"
      :visible="confirmVisible"
      @ok="confirmUpdate"
      @cancel="cancelUpdate"
      okText="确认"
      cancelText="取消"
      :width="400"
    >
      <p>确认将 {{ currentEditRecord.username }} 的每日预算修改为 {{ newBudgetValue }} 元吗？</p>
    </a-modal>
    
    <!-- 确认删除预算的小弹窗 -->
    <a-modal
      title="确认删除预算"
      :visible="deleteConfirmVisible"
      @ok="confirmDelete"
      @cancel="cancelDelete"
      okText="确认删除"
      cancelText="取消"
      :width="400"
    >
      <p>确认删除 {{ currentEditRecord.username }} 的预算限制吗？删除后将不再限制该用户的预算。</p>
    </a-modal>
  </div>
</template>

<script>
import { adsAdvertisingBudget } from "@/api/api";

export default {
  name: "SelfAdsBudgetModal",
  data() {
    return {
      visible: false,
      confirmLoading: false,
      defaultActiveKey: "tiktok",
      keyword: "",
      currentPlatform: "tiktok",
      
      tabList: [
        { platform: "tiktok", name: "抖音" },
        { platform: "adq", name: "ADQ" }
      ],
      
      columns: [
        {
          title: "姓名",
          dataIndex: "username",
          key: "username",
          width: 120
        },
        {
          title: "每日预算(元)",
          dataIndex: "budget",
          key: "budget",
          width: 150,
          scopedSlots: { customRender: "budget" }
        },
        {
          title: "今日已充金额(元)",
          dataIndex: "today_transfer_money",
          key: "today_transfer_money",
          width: 150,
          scopedSlots: { customRender: "today_transfer_money" }
        }
      ],
      
      tableData: [],
      tableLoading: false,
      
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条数据`
      },
      
      // 确认修改相关
      confirmVisible: false,
      
      // 确认删除相关
      deleteConfirmVisible: false,
      
      currentEditRecord: {},
      newBudgetValue: null,
      originalBudgetValue: null,
      
      // 记录每个控件的原始值，用于正确处理焦点和失焦事件
      focusedBudgetValues: {},
      editingRecords: new Set() // 记录正在编辑的记录ID
    };
  },
  
  methods: {
    openModal() {
      this.visible = true;
      // 重置到默认标签页
      this.defaultActiveKey = "tiktok";
      this.currentPlatform = "tiktok";
      this.keyword = "";
      this.pagination.current = 1;
      this.loadBudgetData();
    },
    
    handleOk() {
      this.visible = false;
    },
    
    onTabChange(activeKey) {
      this.currentPlatform = activeKey;
      this.keyword = "";
      this.pagination.current = 1;
      this.loadBudgetData();
    },
    
    searchQuery() {
      this.pagination.current = 1;
      this.loadBudgetData();
    },
    
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.loadBudgetData();
    },
    
    loadBudgetData() {
      this.tableLoading = true;
      
      const params = {
        platform: this.currentPlatform,
        page: this.pagination.current,
        limit: this.pagination.pageSize
      };
      
      if (this.keyword) {
        params.username = this.keyword;
      }
      
      adsAdvertisingBudget.index(params).then(res => {
        this.tableLoading = false;
        if (res.code === 200) {
          this.tableData = res.data.list || [];
          this.pagination.total = parseInt(res.data.totalCount) || 0;
          
          // 确保 budget 字段为数字类型，便于编辑
          this.tableData.forEach(item => {
            if (item.budget === "" || item.budget === null || item.budget === undefined) {
              item.budget = null;
            } else {
              item.budget = parseInt(item.budget);
            }
          });
        } else {
          this.$message.error(res.message || "获取数据失败");
        }
      }).catch(error => {
        this.tableLoading = false;
        this.$message.error("获取数据失败");
        console.error(error);
      });
    },
    
    onBudgetFocus(record) {
      // 当预算输入框获得焦点时，记录当前值
      const recordId = record.member_id;
      this.focusedBudgetValues[recordId] = record.budget;
      this.editingRecords.add(recordId);
    },
    
    onBudgetBlur(record) {
      // 当预算输入框失焦时，判断是否需要显示确认框
      const recordId = record.member_id;
      const newValue = record.budget;
      const originalValue = this.focusedBudgetValues[recordId];
      
      // 移除编辑状态
      this.editingRecords.delete(recordId);
      
      // 比较值是否发生变化
      const isChanged = (newValue !== originalValue) && 
                       !((newValue === null || newValue === undefined) && 
                         (originalValue === null || originalValue === undefined));
      
      if (isChanged) {
        this.currentEditRecord = record;
        this.newBudgetValue = newValue;
        this.originalBudgetValue = originalValue;
        
        // 判断是删除操作还是修改操作
        const isDeleteOperation = (originalValue !== null && originalValue !== undefined) && 
                                 (newValue === null || newValue === undefined);
        
        if (isDeleteOperation) {
          // 显示删除确认弹窗
          this.deleteConfirmVisible = true;
        } else {
          // 显示修改确认弹窗
          this.confirmVisible = true;
        }
      }
    },
    
    confirmUpdate() {
      const params = {
        promote_user_id: this.currentEditRecord.member_id,
        platform: this.currentPlatform,
        budget: this.newBudgetValue
      };
      
      // 如果有 budget_id 且不为 0，则传递 id
      if (this.currentEditRecord.budget_id && this.currentEditRecord.budget_id !== 0) {
        params.id = this.currentEditRecord.budget_id;
      }
      
      adsAdvertisingBudget.updateData(params).then(res => {
        if (res.code === 200) {
          this.$message.success("预算修改成功");
          this.confirmVisible = false;
          // 更新焦点值缓存
          this.focusedBudgetValues[this.currentEditRecord.member_id] = this.newBudgetValue;
          // 重新加载数据
          this.loadBudgetData();
        } else {
          this.$message.error(res.message || "修改失败");
          // 恢复原始值
          this.currentEditRecord.budget = this.originalBudgetValue;
        }
      }).catch(error => {
        this.$message.error("修改失败");
        console.error(error);
        // 恢复原始值
        this.currentEditRecord.budget = this.originalBudgetValue;
      });
    },
    
    cancelUpdate() {
      // 恢复原始值
      this.currentEditRecord.budget = this.originalBudgetValue;
      this.confirmVisible = false;
    },
    
    confirmDelete() {
      // 确认删除预算
      if (!this.currentEditRecord.budget_id || this.currentEditRecord.budget_id === 0) {
        this.$message.warning("该用户暂无预算记录，无需删除");
        this.deleteConfirmVisible = false;
        return;
      }
      
      const params = {
        id: this.currentEditRecord.budget_id
      };
      
      adsAdvertisingBudget.delete(params).then(res => {
        if (res.code === 200) {
          this.$message.success("预算删除成功");
          this.deleteConfirmVisible = false;
          // 更新焦点值缓存为空值
          this.focusedBudgetValues[this.currentEditRecord.member_id] = null;
          // 重新加载数据
          this.loadBudgetData();
        } else {
          this.$message.error(res.message || "删除失败");
          // 恢复原始值
          this.currentEditRecord.budget = this.originalBudgetValue;
        }
      }).catch(error => {
        this.$message.error("删除失败");
        console.error(error);
        // 恢复原始值
        this.currentEditRecord.budget = this.originalBudgetValue;
      });
    },
    
    cancelDelete() {
      // 恢复原始值
      this.currentEditRecord.budget = this.originalBudgetValue;
      this.deleteConfirmVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.budgetBox {
  display: flex;
  flex-direction: column;
  
  .ant-table-wrapper {
    flex: 1;
    height: 0;
    overflow: auto;
  }
}

/deep/ .ant-input-number {
  width: 120px;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 8px 16px;
}
</style>
