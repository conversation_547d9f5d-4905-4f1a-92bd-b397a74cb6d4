<?php

namespace backendapi\services\promote\transfermoneyv2;

use backendapi\models\promote\AdsMainBody;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use backendapi\services\promote\transfermoneyv2\validator\TimeValidator;
use backendapi\services\promote\transfermoneyv2\validator\AccountValidator;
use backendapi\services\promote\transfermoneyv2\validator\AmountValidator;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use backendapi\services\promote\transfermoneyv2\config\ConfigManager;
use auth\services\promote\AdsAdvertisingBudgetService;
use common\enums\AdsAccountSubStatusEnum;
use common\enums\reportEnum;
use common\enums\StatusEnum;
use common\models\backend\Member;
use services\UserService;
use common\models\common\AdsAccountSub;
use common\models\promote\AdsAdvertisingBudget;
use common\models\promote\AdsTransferMoneyRecord;
use common\queues\TransferMoneyJobV2;
use Exception;
use Yii;

/**
 * 统一业务服务类 TransferMoneyServiceV2
 * 
 * 整合所有前面实现的组件，基于现有 TransferMoneyBatchService 的业务逻辑
 * 支持四种充值模式：正常充值、批量充值、定时充值、加粉充值
 * 
 * 核心特性：
 * - 使用平台适配器模式支持多平台
 * - 使用验证器链进行数据验证
 * - 使用缓存管理器管理充值限制
 * - 保持与现有系统的完全兼容性
 */
class TransferMoneyServiceV2
{
    /**
     * @var PlatformFactory 平台工厂
     */
    private $platformFactory;

    /**
     * @var TransferCacheManager 缓存管理器
     */
    private $cacheManager;

    /**
     * @var array 平台适配器缓存 [platform => adapter]
     */
    private $adapters = [];

    // 业务属性 - 基于现有 TransferMoneyBatchService
    private $accessToken = '';
    public $advertiser_id = '';
    private $target_advertiser_id = '';
    private $target_advertiser_name = '';
    private $amount = 0;

    private $user_id;
    private $user_name;
    public $mainBody;
    public $platform = '';
    public $organization_id = '';
    public $insufficientBalance;

    // 平台限额配置 - 从配置文件动态获取
    private $platformLimits = [];

    // 错误码体系 - 保持与现有系统一致
    public $code = 422;
    public $success_code = 200;
    private $time_code = 100;
    public $success_insufficient_balance_code = 201;
    private $error_code_it = 422;
    private $error_code_promote = 423;
    private $error_code_insufficient_balance = 424;
    private $error_code_budget_limit = 425; // 充值限额超出错误码
    private $error_code_account_status = 426; // 账户状态错误码

    // 缓存相关

    private $timeRecharge = '';

    /**
     * 构造函数
     *
     * @param PlatformFactory|null $platformFactory 平台工厂
     * @param TransferCacheManager|null $cacheManager 缓存管理器
     */
    public function __construct(
        ?PlatformFactory $platformFactory = null,
        ?TransferCacheManager $cacheManager = null
    ) {
        $this->platformFactory = $platformFactory ?: new PlatformFactory();
        $this->cacheManager = $cacheManager ?: new TransferCacheManager();
        // 初始化平台限额配置
        $this->initializePlatformLimits();
    }

    /**
     * 主入口方法 - 基于新架构设计，支持多平台混合充值
     *
     * @param array $params 充值参数
     * @return array|string 充值结果
     * @throws Exception
     */
    public function run($params)
    {

        // 时间限制检查
        $this->timeLimit();

        // 参数处理
        $result = $this->dealParams($params);

        // 账户验证和平台检测（支持多平台混合充值）
        $accountPlatformMap = $this->verificationAccountWithPlatforms($result);
        $data['target_advertiser_ids'] = array_keys($accountPlatformMap);
        if (empty($data['target_advertiser_ids'])) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户ID不能为空');
        }

        $data['amount'] = $this->verificationAmount($result, $accountPlatformMap);

        // 账户状态验证
        $this->checkAccountStatus($data['target_advertiser_ids']);

        // 充值限额检查
        $this->checkBudgetLimit($data['target_advertiser_ids'], $data['amount'], $accountPlatformMap);

        // 定时充值判断
        $this->isTimeRecharge($result);
        $data['user_name'] = $this->user_name;
        $data['account_platform_map'] = $accountPlatformMap; // 传递平台映射信息

        if ($this->timeRecharge) {
            // 定时充值流程
            $this->code = $this->time_code;
            $data['isTimeRecharge'] = true;
            $data['execute_time'] = $this->timeRecharge;
            TransferMoneyJobV2::addJob($data);
            return '定时充值操作成功';
        } else {
            // 立即充值流程
            $this->code = $this->success_code;
            $data['execute_time'] = time();
            $data['isTimeRecharge'] = false;
            TransferMoneyJobV2::addJob($data);
            return ['200' => ['code' => $this->success_code, 'msg' => '充值成功']];
        }
    }

    /**
     * 执行充值逻辑 - 支持多平台混合充值的新架构
     *
     * @param array $data 充值数据
     * @return array 充值结果
     * @throws Exception
     */
    public function execute($data)
    {
        // 新架构移除批量限制，支持更大规模充值
        // 旧版限制：if (count($data['target_advertiser_ids']) > 50) throw new Exception('充值一次最多只能50个户');

        $this->amount = $data['amount'];
        $result = [];
        $num = 1;

        // 获取账户平台映射信息
        $accountPlatformMap = $data['account_platform_map'] ?? [];

        foreach ($data['target_advertiser_ids'] as $target_advertiser_id) {
            // 初始化
            $this->initialize();

            // 频率控制 - 每充值10个户睡眠500毫秒
            if ($num % 10 === 0) {
                usleep(500000);
            }

            try {
                // 设置当前账户的平台信息（支持多平台混合充值）
                if (isset($accountPlatformMap[$target_advertiser_id])) {
                    $this->platform = $accountPlatformMap[$target_advertiser_id];
                }

                // 目标账户设置（会重新设置平台，确保数据一致性）
                $this->setTargetAdvertiserIds($target_advertiser_id);

                // 金额限制检查
                $this->amountLimit();

                // 执行充值
                $this->transferMoney();

                $num++;

                // 成功处理
                $this->success();
                $msg = '充值成功';

            } catch (Exception $e) {
                $msg = $e->getMessage();
                Yii::error($msg, 'TransferMoneyJobV2Execute');
            }

            $result[$this->code][] = [
                'msg' => $msg,
                'target_advertiser_id' => $this->target_advertiser_id,
                'target_advertiser_name' => $this->target_advertiser_name,
                'main_body' => $this->mainBody,
                'advertiser_id' => $this->advertiser_id,
                'platform' => $this->platform, // 新增平台信息
                'insufficientNalance' => $this->insufficientBalance
            ];
        }

        return $result;
    }

    /**
     * 执行多平台混合充值 - 新架构特性
     *
     * @param array $data 充值数据
     * @return array 充值结果
     * @throws Exception
     */
    public function executeMixedPlatform($data)
    {
        $accountPlatformMap = $data['account_platform_map'] ?? [];

        if (empty($accountPlatformMap)) {
            // 如果没有平台映射信息，回退到原有逻辑
            return $this->execute($data);
        }

        // 按平台分组账户
        $platformGroups = [];
        foreach ($accountPlatformMap as $accountId => $platform) {
            $platformGroups[$platform][] = $accountId;
        }

        $allResults = [];

        // 分平台处理充值
        foreach ($platformGroups as $platform => $accountIds) {
            $platformData = $data;
            $platformData['target_advertiser_ids'] = $accountIds;

            // 为当前平台创建账户映射
            $platformAccountMap = [];
            foreach ($accountIds as $accountId) {
                $platformAccountMap[$accountId] = $platform;
            }
            $platformData['account_platform_map'] = $platformAccountMap;

            try {
                $platformResults = $this->execute($platformData);
                $allResults = $this->mergePlatformResults($allResults, $platformResults, $platform);
            } catch (Exception $e) {
                // 记录平台充值失败，但继续处理其他平台
                Yii::error("平台 {$platform} 充值失败: " . $e->getMessage(), 'transfer_money_mixed_platform');

                // 将失败信息添加到结果中
                foreach ($accountIds as $accountId) {
                    $allResults[$this->error_code_it][] = [
                        'msg' => "平台 {$platform} 充值失败: " . $e->getMessage(),
                        'target_advertiser_id' => $accountId,
                        'target_advertiser_name' => '',
                        'main_body' => '',
                        'advertiser_id' => '',
                        'platform' => $platform,
                        'insufficientNalance' => 0
                    ];
                }
            }
        }

        return $allResults;
    }

    /**
     * 合并多平台充值结果
     *
     * @param array $allResults 总结果
     * @param array $platformResults 单平台结果
     * @param string $platform 平台名称
     * @return array 合并后的结果
     */
    private function mergePlatformResults($allResults, $platformResults, $platform)
    {
        foreach ($platformResults as $code => $results) {
            if (!isset($allResults[$code])) {
                $allResults[$code] = [];
            }

            // 为每个结果添加平台标识
            foreach ($results as $result) {
                $result['platform'] = $platform;
                $allResults[$code][] = $result;
            }
        }

        return $allResults;
    }

    /**
     * 单个账户充值 - 基于现有 TransferMoneyBatchService::transferMoney() 方法逻辑
     *
     * @return bool 充值是否成功
     * @throws Exception
     */
    public function transferMoney()
    {
        $balance = $this->getBalance();
        $this->insufficientBalance = $balance - $this->amount;

        // 获取平台适配器并执行充值
        $adapter = $this->getCurrentAdapter();

        try {
            $result = $adapter->transferMoney(
                $this->accessToken,
                $this->organization_id,
                $this->advertiser_id,
                $this->target_advertiser_id,
                $this->amount
            );

            // 检查充值结果
            if (!isset($result['code']) || $result['code'] != 0) {
                $this->code = $this->error_code_it;
                $errorMessage = $result['message'] ?? $result['message_cn'] ?? '未知错误';
                throw new Exception('充值失败：' . $errorMessage);
            }

        } catch (Exception $e) {
            $this->code = $this->error_code_it;
            // 如果是适配器抛出的异常，直接重新抛出
            if (strpos($e->getMessage(), '充值失败') === 0) {
                throw $e;
            }
            // 否则包装异常信息
            throw new Exception('充值失败：' . $e->getMessage());
        }

        // 更新余额缓存
        $this->cacheManager->setBalance($this->advertiser_id, $this->insufficientBalance);

        return true;
    }

    /**
     * 获取账户余额
     *
     * @return float 账户余额
     * @throws Exception
     */
    public function getBalance()
    {
        // 先尝试从缓存获取
        $balance = $this->cacheManager->getBalance($this->advertiser_id);

        if ($balance === null) {
            // 缓存中没有余额，从平台获取
            $balance = $this->getBalanceFromPlatform();
            // 缓存余额
            $this->cacheManager->setBalance($this->advertiser_id, $balance);
        }

        // 余额充足，直接返回
        if ($this->amount <= $balance) {
            return $balance;
        }

        // 余额不足时，重新从平台获取最新余额（可能推广人员已在平台充值）
        Yii::info("账户 {$this->advertiser_id} 缓存余额不足({$balance}元)，重新从平台获取最新余额", 'transfer_money_balance');
        
        try {
            $latestBalance = $this->getBalanceFromPlatform();
            
            // 更新缓存
            $this->cacheManager->setBalance($this->advertiser_id, $latestBalance);
            
            // 记录余额更新日志
            if ($latestBalance != $balance) {
                Yii::info("账户 {$this->advertiser_id} 余额已更新: {$balance}元 -> {$latestBalance}元", 'transfer_money_balance');
            }
            
            // 最新余额充足，返回最新余额
            if ($this->amount <= $latestBalance) {
                return $latestBalance;
            }
            
            // 即使是最新余额仍然不足，删除缓存并抛出异常
            $this->cacheManager->deleteBalance($this->advertiser_id);
            $this->code = $this->error_code_insufficient_balance;

            $errorMsg = '充值失败，主体：' . $this->mainBody . '（' . $this->advertiser_id . '）， ' .
                       '账户余额不足，剩余：' . $latestBalance . '元，请联系推广管理人员处理';
            throw new Exception($errorMsg);
            
        } catch (Exception $e) {
            // 如果是余额不足的异常，直接重新抛出
            if ($this->code == $this->error_code_insufficient_balance) {
                throw $e;
            }
            
            // 如果是查询余额的技术异常，删除缓存并抛出异常
            $this->cacheManager->deleteBalance($this->advertiser_id);
            $this->code = $this->error_code_it;
            throw new Exception('重新查询户' . $this->advertiser_id . '最新余额报错: ' . $e->getMessage());
        }
    }

    /**
     * 从平台获取账户余额
     * 
     * @return float 账户余额
     * @throws Exception
     */
    private function getBalanceFromPlatform()
    {
        $adapter = $this->getCurrentAdapter();

        try {
            $balance = $adapter->getBalance($this->accessToken, $this->advertiser_id);
            return $balance;
        } catch (Exception $e) {
            $this->code = $this->error_code_it;
            throw new Exception('查询户' . $this->advertiser_id . '可用余额报错: ' . $e->getMessage());
        }
    }

    /**
     * 查询所有账户余额
     *
     * @return string 账户余额信息
     * @throws Exception
     */
    public function getAccountBalance()
    {
        try {
            $list = $this->accountList();
            if (empty($list)) {
                throw new Exception('要查询的账户不能为空');
            }

            $content = '';
            foreach ($list as $name => $account) {
                $this->advertiser_id = $account;
                $this->mainBody = $name;
                $this->setToken();
                $this->setPlatform();

                // 使用平台适配器查询余额
                $adapter = $this->getCurrentAdapter();

                try {
                    $balance = $adapter->getBalance($this->accessToken, $this->advertiser_id);
                    $content .= $name . '：' . $balance . PHP_EOL;
                } catch (Exception $e) {
                    throw new Exception('查询户' . $this->advertiser_id . '可用余额报错: ' . $e->getMessage());
                }
            }

            return $content;

        } catch (Exception $e) {
            throw new Exception('查询余额失败，原因：' . $e->getMessage());
        }
    }

    /**
     * 初始化 - 基于现有 TransferMoneyBatchService::initialize() 方法逻辑
     */
    public function initialize()
    {
        $this->target_advertiser_id = '';
        $this->target_advertiser_name = '';
        $this->mainBody = '';
        $this->advertiser_id = '';
        $this->accessToken = '';
        $this->code = 422;
        $this->insufficientBalance = '';
    }

    /**
     * 时间限制检查
     *
     * @throws Exception
     */
    public function timeLimit()
    {
        $timeRestrictions = ConfigManager::getTransferLimitsConfig('time_restrictions.forbidden_hours', []);

        if (empty($timeRestrictions) || !($timeRestrictions['enabled'] ?? true)) {
            return; // 时间限制未启用
        }

        $currentTime = date('H:i');
        $startTime = $timeRestrictions['start'] ?? '02:00';
        $endTime = $timeRestrictions['end'] ?? '06:30';

        if ($currentTime >= $startTime && $currentTime <= $endTime) {
            $this->code = $this->error_code_promote;
            throw new Exception('"' . $startTime . '到' . $endTime . '"时间段不可充值');
        }
    }

    /**
     * 参数处理 - 基于现有 TransferMoneyBatchService::dealParams() 方法逻辑
     * 
     * @param array $params 输入参数
     * @return array 处理后的参数
     * @throws Exception
     */
    public function dealParams($params = '')
    {
        if (empty($params)) {
            $this->code = $this->error_code_promote;
            throw new Exception('充值数据不能为空');
        }

        $this->user_id = $params['user_id'];
        $this->user_name = $params['user_name'];

        $parts = explode("\n", $params['data']);
        $list = [];
        $keyList = [];
        
        foreach ($parts as $part) {
            if (strpos($part, '：') === false) {
                continue;
            }
            list($key, $value) = explode('：', $part, 2);
            $key = trim($key);
            $value = trim($value);
            $list[$key] = $value;
            $keyList[] = $key;
        }
        
        $fields = ['账户ID', '转账金额'];
        $diff = array_diff($fields, $keyList);
        if (!empty($diff)) {
            $this->code = $this->error_code_promote;
            throw new Exception('数据格式有误，请认真审查');
        }

        // 处理账户ID和金额，返回结构化数据
        // $target_advertiser_ids = explode('、', $list['账户ID']);
        // $target_advertiser_ids = array_filter($target_advertiser_ids, function ($value) {
        //     return $value !== null && $value !== '';
        // });
        // $target_advertiser_ids = array_unique($target_advertiser_ids);

        // $result = $list;
        // $result['target_advertiser_ids'] = $target_advertiser_ids;
        // $result['amount'] = (int)$list['转账金额'];

        return $list;
    }

    /**
     * 账户验证 - 支持多平台混合充值的新架构
     *
     * @param array $data 验证数据
     * @return array 账户ID到平台的映射 ['account_id' => 'platform']
     * @throws Exception
     */
    public function verificationAccountWithPlatforms($data)
    {
        $target_advertiser_ids = explode('、', $data['账户ID']);
        $target_advertiser_ids = array_filter($target_advertiser_ids, function ($value) {
            return $value !== null && $value !== '';
        });

        $target_advertiser_ids = array_unique($target_advertiser_ids);

        if (empty($target_advertiser_ids)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户ID不能为空');
        }

        // 获取每个账户的平台信息（支持多平台混合充值）
        $accountPlatforms = AdsAccountSub::find()->alias('as')
            ->select('as.sub_advertiser_id, a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $target_advertiser_ids])
            ->asArray()
            ->all();

        if (empty($accountPlatforms)) {
            $this->code = $this->error_code_promote;
            throw new Exception('未找到有效的账户信息');
        }

        // 构建账户到平台的映射
        $accountPlatformMap = [];
        foreach ($accountPlatforms as $account) {
            $accountPlatformMap[$account['sub_advertiser_id']] = $account['platform'];
        }

        // 检查是否有账户未找到平台信息
        $missingAccounts = array_diff($target_advertiser_ids, array_keys($accountPlatformMap));
        if (!empty($missingAccounts)) {
            $this->code = $this->error_code_promote;
            throw new Exception('以下账户未找到平台信息：' . implode('、', $missingAccounts));
        }

        return $accountPlatformMap;
    }

    /**
     * 金额验证 - 支持多平台混合充值的新架构
     *
     * @param array $data 验证数据
     * @param array $accountPlatformMap 账户到平台的映射
     * @return float 验证后的金额
     * @throws Exception
     */
    public function verificationAmount($data, $accountPlatformMap)
    {
        $amount = (float)$data['转账金额'];

        if ($amount <= 0) {
            $this->code = $this->error_code_promote;
            throw new Exception('单次充值金额必须大于0');
        }

        return $this->verificationAmountForMultiPlatform($amount, $accountPlatformMap);
    }

    /**
     * 多平台混合充值的金额验证
     *
     * @param float $amount 充值金额
     * @param array $accountPlatformMap 账户到平台的映射
     * @return float 验证后的金额
     * @throws Exception
     */
    private function verificationAmountForMultiPlatform($amount, $accountPlatformMap)
    {
        // 获取所有涉及平台的限制
        $platforms = array_unique(array_values($accountPlatformMap));
        $platformLimits = [];

        foreach ($platforms as $platform) {
            $platformLimits[$platform] = $this->getPlatformSingleLimit($platform);
        }

        // 检查金额是否超过任一平台的限制
        $minLimit = min($platformLimits);

        if ($amount > $minLimit) {
            // 如果超过最小限制，需要详细检查每个平台
            $exceedPlatforms = [];
            foreach ($platformLimits as $platform => $limit) {
                if ($amount > $limit) {
                    $exceedPlatforms[] = $platform . '(限制:' . $limit . ')';
                }
            }

            if (!empty($exceedPlatforms)) {
                $this->code = $this->error_code_promote;
                throw new Exception('充值金额超过以下平台限制: ' . implode('、', $exceedPlatforms));
            }
        }

        return $amount;
    }

    /**
     * 定时充值判断 - 基于现有 TransferMoneyBatchService::isTimeRecharge() 方法逻辑
     * 
     * @param array $data 验证数据
     * @throws Exception
     */
    public function isTimeRecharge($data)
    {
        if (!isset($data['定时充值'])) {
            return;
        }

        $timeRechargeDate = $data['定时充值'];

        if (empty($timeRechargeDate)) {
            $this->code = $this->error_code_promote;
            throw new Exception('定时充值时间不能为空');
        }

        $timeRecharge = strtotime($timeRechargeDate);
        if ($timeRecharge <= time()) {
            $this->code = $this->error_code_promote;
            throw new Exception('定时充值时间不能小于当前时间');
        }

        $currentDate = date('Y-m-d', time());
        if ($timeRecharge >= strtotime($currentDate . '+2 day')) {
            $this->code = $this->error_code_promote;
            throw new Exception('定时充值时间只能在今天和明天之间');
        }

        $this->timeRecharge = $timeRecharge;
    }

    /**
     * 设置目标账户 - 基于现有 TransferMoneyBatchService::setTargetAdvertiserIds() 方法逻辑
     * 
     * @param string $target_advertiser_id 目标账户ID
     * @throws Exception
     */
    public function setTargetAdvertiserIds($target_advertiser_id)
    {
        $this->target_advertiser_id = $target_advertiser_id;
        
        $info = AdsMainBody::find()
            ->alias('mb')->select('aas.id,mb.name,mb.sub_advertiser_id,aas.sub_advertiser_name')
            ->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.main_body_id = mb.id')
            ->where(['aas.sub_advertiser_id' => $this->target_advertiser_id])
            ->asArray()
            ->one();
            
        if (empty($info)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户ID不存在系统中，请核对下账户ID是否正确');
        }

        $this->mainBody = $info['name'];
        if (empty($this->mainBody)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户主体未绑定，请联系管理员绑定！');
        }
        
        $this->advertiser_id = $info['sub_advertiser_id'];
        $this->target_advertiser_name = $info['sub_advertiser_name'];
        $this->setToken();
        $this->setPlatform();
    }

    /**
     * 设置访问令牌
     * 
     * @throws Exception
     */
    public function setToken()
    {
        $info = AdsAccountSub::find()->alias('as')
            ->select('a.access_token,a.advertiser_id as organization_id,a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $this->advertiser_id])
            ->asArray()
            ->one();

        if (empty($info)) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '不存在erp系统中，请联系技术部处理');
        }

        if (empty($info['access_token'])) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '的token不存在erp系统中，请联系技术部处理');
        }

        if (empty($info['organization_id']) && $info['platform'] == reportEnum::TIKTOL) {
            $this->code = $this->error_code_it;
            throw new Exception('该备用金户：' . $this->advertiser_id . '的组织ID不存在erp系统中，请联系技术部处理');
        }

        $this->accessToken = $info['access_token'];
        $this->organization_id = $info['organization_id'];
    }

    /**
     * 设置平台 - 新架构优化，支持多平台混合充值
     *
     * @throws Exception
     */
    public function setPlatform()
    {
        // 新架构：如果 advertiser_id 未设置，跳过平台设置
        // 这解决了在 run() 方法中过早调用 setPlatform() 的问题
        if (empty($this->advertiser_id)) {
            return;
        }

        $platform = AdsAccountSub::find()->alias('as')
            ->select('a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $this->advertiser_id])
            ->scalar();

        if (empty($platform)) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '的平台不存在erp系统中，请联系技术部处理');
        }

        $this->platform = $platform;
    }

    /**
     * 金额限制检查
     * 
     * @return bool
     * @throws Exception
     */
    public function amountLimit()
    {
        $maxTotalAmount = $this->getPlatformHourlyLimit($this->platform);

        return $this->cacheManager->checkHourlyLimit(
            $this->target_advertiser_id,
            $this->amount,
            $maxTotalAmount
        );
    }

    /**
     * 成功处理
     */
    public function success()
    {
        // 记录到Redis缓存（保持原有逻辑）
        $this->cacheManager->recordSuccessfulTransfer(
            $this->target_advertiser_id,
            $this->amount,
            $this->user_name
        );

        // 保存充值记录到数据库
        $this->saveTransferRecord();

        if ($this->insufficientBalance <= 3000) {
            $this->code = $this->success_insufficient_balance_code;
        } else {
            $this->code = $this->success_code;
        }
    }

    /**
     * 获取账户列表
     * 
     * @return array
     */
    public function accountList()
    {
        $key = AdsMainBody::$reidsKey;
        $redis = Yii::$app->cache;
        $list = $redis->get($key);
        
        if ($list) {
            return $list;
        }

        $list = AdsMainBody::find()
            ->select('name,sub_advertiser_id')
            ->where(['<>', 'sub_advertiser_id', ''])
            ->andWhere(['status' => 1])
            ->asArray()->all();

        $list = array_column($list, 'sub_advertiser_id', 'name');
        $redis->set($key, $list, 3600);
        
        return $list;
    }

    /**
     * 结果数据处理 - 基于现有 TransferMoneyBatchService::resRealData() 方法逻辑
     * 
     * @param array $res 原始结果数据
     * @return array 处理后的结果数据
     */
    public function resRealData($res)
    {
        $content = [];
        
        foreach ($res as $code => $v) {
            if ($code == 200) {
                $content[$code] = '充值成功';
            }
            
            if ($code == 201) {
                $mainBodyBalances = [];
                foreach ($v as $entry) {
                    $mainBody = $entry['main_body'] ?? '';
                    $balance = $entry['insufficientNalance'] ?? null;
                    $advertiserId = $entry['advertiser_id'] ?? '';

                    if ($mainBody && $balance !== null) {
                        if (!isset($mainBodyBalances[$mainBody])) {
                            $mainBodyBalances[$mainBody] = [
                                '余额' => $balance,
                                'advertiser_id' => $advertiserId,
                            ];
                        } else {
                            $mainBodyBalances[$mainBody]['余额'] = min($mainBodyBalances[$mainBody]['余额'], $balance);
                        }
                    }
                }
                
                $index = 0;
                $total = count($mainBodyBalances);
                foreach ($mainBodyBalances as $mainBody => $data) {
                    $content[$code] .= '主体：' . $mainBody . ',账户ID：' . $data['advertiser_id'] . 
                                     ' 的备用金仅剩：' . $data['余额'] . '元，请及时充值';
                    if ($index < $total) {
                        $content[$code] .= PHP_EOL;
                    }
                }
            }

            if ($code != 200 && $code != 201) {
                $index = 0;
                $total = count($v);
                foreach ($v as $entry) {
                    $target_advertiser_name = $entry['target_advertiser_name'] ? '(' . $entry['target_advertiser_name'] . ')' : "";
                    $content[$code] .= '账户：' . $entry['target_advertiser_id'] . $target_advertiser_name . 
                                      ',失败原因：' . $entry['msg'];
                    if ($index < $total) {
                        $content[$code] .= PHP_EOL;
                    }
                }
            }
        }

        $list = [];
        foreach ($content as $code => $msg) {
            $list[$code] = [
                'code' => $code,
                'msg' => $msg
            ];
        }

        return $list;
    }

    /**
     * 加粉后账户自动充值 - 基于现有 CusCustomerUser::afterSave() 方法逻辑
     * 
     * @param string $subAdvertiserId 子账户ID
     * @return bool 是否成功
     */
    public function addFansRecharge($subAdvertiserId)
    {
        try {
            // 检查充值频次
            if (!$this->checkAddFansTransferFrequency($subAdvertiserId)) {
                return false;
            }

            $transferData = [
                'target_advertiser_ids' => [$subAdvertiserId],
                'amount' => 50,
                'user_name' => '系统自动充值',
            ];

            // 添加到队列
            TransferMoneyJobV2::addJob([
                'data' => $transferData,
                'execute_time' => time(),
                'isTimeRecharge' => false,
                'isSendMessage' => false
            ]);

            return true;
            
        } catch (Exception $e) {
            Yii::error('加粉后账户自动充值失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查加粉充值频次
     * 
     * @param string $subAdvertiserId 子账户ID
     * @return bool 是否允许充值
     */
    public function checkAddFansTransferFrequency($subAdvertiserId)
    {
        $today = date('Y-m-d');
        
        // 检查当天是否已被限制
        if ($this->cacheManager->isAddFansRestricted($today, $subAdvertiserId)) {
            return false;
        }

        // 检查5分钟内充值次数
        $count = $this->cacheManager->getAddFansTransferCount($subAdvertiserId);
        if ($count >= 5) {
            // 添加到当天限制列表
            $this->cacheManager->addAddFansRestrictedAccount($today, $subAdvertiserId);
            
            // 发送通知
            $error = '账户ID：' . $subAdvertiserId . PHP_EOL;
            $error .= '加粉异常,在一分钟内充值超过5次，已被限制充值';
            Yii::$app->feishuNotice->text($error);
            
            return false;
        }

        // 增加计数
        $this->cacheManager->incrementAddFansTransferCount($subAdvertiserId);
        return true;
    }

    /**
     * 获取当前平台适配器 - 支持多平台混合充值
     *
     * @return PlatformAdapterInterface
     * @throws Exception
     */
    private function getCurrentAdapter()
    {
        $platformType = $this->platform == reportEnum::TIKTOL ? 'tiktok' : 'adq';

        // 如果该平台的适配器还未创建，则创建并缓存
        if (!isset($this->adapters[$platformType])) {
            $this->adapters[$platformType] = $this->platformFactory->create($platformType);
        }

        return $this->adapters[$platformType];
    }

    /**
     * 保存充值记录到数据库
     * 
     * @return bool 是否保存成功
     * @throws Exception
     */
    private function saveTransferRecord()
    {
        try {
            $record = new AdsTransferMoneyRecord();
            
            // 生成充值序号
            $record->serial_number = $this->generateSerialNumber();
            
            // 设置基本信息
            $record->user_id = $this->user_id;
            $record->user_name = $this->user_name;
            $record->transfer_advertiser_id = $this->advertiser_id;
            $record->target_advertiser_id = $this->target_advertiser_id;
            $record->platform = $this->platform;
            $record->amount = $this->amount;
            $record->status = 1; // 1表示充值成功
            
            if (!$record->save()) {
                Yii::error('保存充值记录失败: ' . json_encode($record->errors), 'transfer_money_service');
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            Yii::error('保存充值记录异常: ' . $e->getMessage(), 'transfer_money_service');
            return false;
        }
    }

    /**
     * 生成充值序号
     * 
     * @return string 充值序号
     */
    private function generateSerialNumber()
    {
        $date = date('Ymd');
        $microtime = explode(' ', microtime());
        $microtime = $microtime[1] . substr($microtime[0], 2, 6);
        
        return 'TM' . $date . $microtime;
    }

    // Getter 和 Setter 方法
    
    /**
     * 获取平台工厂
     *
     * @return PlatformFactory
     */
    public function getPlatformFactory()
    {
        return $this->platformFactory;
    }

    /**
     * 获取缓存管理器
     *
     * @return TransferCacheManager
     */
    public function getCacheManager()
    {
        return $this->cacheManager;
    }

    /**
     * 获取错误码
     *
     * @return int
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * 设置错误码
     *
     * @param int $code
     */
    public function setCode($code)
    {
        $this->code = $code;
    }

    /**
     * 获取成功码
     *
     * @return int
     */
    public function getSuccessCode()
    {
        return $this->success_code;
    }

    /**
     * 获取时间码
     *
     * @return int
     */
    public function getTimeCode()
    {
        return $this->time_code;
    }

    /**
     * 获取成功但余额不足码
     *
     * @return int
     */
    public function getSuccessInsufficientBalanceCode()
    {
        return $this->success_insufficient_balance_code;
    }

    /**
     * 获取技术错误码
     *
     * @return int
     */
    public function getErrorCodeIt()
    {
        return $this->error_code_it;
    }

    /**
     * 获取推广错误码
     *
     * @return int
     */
    public function getErrorCodePromote()
    {
        return $this->error_code_promote;
    }

    /**
     * 获取余额不足错误码
     *
     * @return int
     */
    public function getErrorCodeInsufficientBalance()
    {
        return $this->error_code_insufficient_balance;
    }

    /**
     * 获取目标账户ID
     *
     * @return string
     */
    public function getTargetAdvertiserId()
    {
        return $this->target_advertiser_id;
    }

    /**
     * 设置目标账户ID
     *
     * @param string $targetAdvertiserId
     */
    public function setTargetAdvertiserId($targetAdvertiserId)
    {
        $this->target_advertiser_id = $targetAdvertiserId;
    }

    /**
     * 获取主体
     *
     * @return string
     */
    public function getMainBody()
    {
        return $this->mainBody;
    }

    /**
     * 获取广告主ID
     *
     * @return string
     */
    public function getAdvertiserId()
    {
        return $this->advertiser_id;
    }

    /**
     * 设置广告主ID
     *
     * @param string $advertiserId
     */
    public function setAdvertiserId($advertiserId)
    {
        $this->advertiser_id = $advertiserId;
    }

    /**
     * 获取金额
     *
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * 设置金额
     *
     * @param float $amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    /**
     * 获取用户名
     *
     * @return string
     */
    public function getUserName()
    {
        return $this->user_name;
    }

    /**
     * 设置用户名
     *
     * @param string $userName
     */
    public function setUserName($userName)
    {
        $this->user_name = $userName;
    }

    /**
     * 获取余额不足金额
     *
     * @return float
     */
    public function getInsufficientBalance()
    {
        return $this->insufficientBalance;
    }

    /**
     * 设置余额不足金额
     *
     * @param float $insufficientBalance
     */
    public function setInsufficientBalance($insufficientBalance)
    {
        $this->insufficientBalance = $insufficientBalance;
    }

    /**
     * 获取定时充值时间
     *
     * @return string
     */
    public function getTimeRecharge()
    {
        return $this->timeRecharge;
    }

    /**
     * 是否启用定时充值
     *
     * @return bool
     */
    public function isTimeRechargeEnabled()
    {
        return !empty($this->timeRecharge);
    }

    /**
     * 获取平台
     *
     * @return string
     */
    public function getPlatform()
    {
        return $this->platform;
    }

    /**
     * 初始化平台限额配置
     * 从配置文件读取平台限额设置
     */
    private function initializePlatformLimits()
    {
        $this->platformLimits = ConfigManager::getPlatformConfig('platform_limits', []);
    }

    /**
     * 获取平台单次充值限额
     *
     * @param string $platform 平台类型
     * @return int 单次充值限额
     */
    private function getPlatformSingleLimit($platform)
    {
        $platformKey = $platform == reportEnum::ADQ ? 'adq' : 'tiktok';
        return $this->platformLimits[$platformKey]['single_recharge_amount'] ?? 1000;
    }

    /**
     * 获取平台小时充值限额
     *
     * @param string $platform 平台类型
     * @return int 小时充值限额
     */
    private function getPlatformHourlyLimit($platform)
    {
        $platformKey = $platform == reportEnum::ADQ ? 'adq' : 'tiktok';
        return $this->platformLimits[$platformKey]['hourly_max_amount'] ?? 3000;
    }

    /**
     * 检查充值限额
     *
     * @param array $targetAdvertiserIds 目标账户ID数组
     * @param float $amount 充值金额
     * @param array $accountPlatformMap 账户平台映射
     * @throws Exception
     */
    private function checkBudgetLimit($targetAdvertiserIds, $amount, $accountPlatformMap)
    {
        try {
            // 根据user_name获取推广人员ID
            $userId = $this->getUserIdByUserName($this->user_name);
            if (empty($userId)) {
                // 如果找不到用户，记录日志但不阻止充值（保持向后兼容）
                Yii::warning("充值限额检查：未找到用户名为 {$this->user_name} 的推广人员", 'budget_limit_check');
                return;
            }

            // 按平台分组检查限额
            $platformGroups = [];
            foreach ($targetAdvertiserIds as $accountId) {
                $platform = $accountPlatformMap[$accountId] ?? '';
                if (!empty($platform)) {
                    $platformGroups[$platform][] = $accountId;
                }
            }

            foreach ($platformGroups as $platform => $accountIds) {
                $this->checkPlatformBudgetLimit($userId, $platform, $amount, $accountIds);
            }

        } catch (Exception $e) {
            // 如果是充值限额异常，直接重新抛出
            if ($this->code == $this->error_code_budget_limit) {
                throw $e;
            }

            // 其他异常记录日志但不阻止充值
            Yii::error("充值限额检查异常: " . $e->getMessage(), 'budget_limit_check');
        }
    }

    /**
     * 根据用户名获取推广人员ID
     *
     * @param string $userName 用户名
     * @return int|null 推广人员ID
     */
    private function getUserIdByUserName($userName)
    {
        if (empty($userName)) {
            return null;
        }

        $member = Member::find()
            ->select('id')
            ->where(['username' => $userName])
            ->andWhere(['current_entity_id' => UserService::getInst()->current_entity_id])
            ->one();

        return $member ? $member->id : null;
    }

    /**
     * 检查指定平台的充值限额
     *
     * @param int $userId 推广人员ID
     * @param string $platform 平台
     * @param float $amount 充值金额
     * @param array $accountIds 账户ID数组
     * @throws Exception
     */
    private function checkPlatformBudgetLimit($userId, $platform, $amount, $accountIds)
    {
        // 获取推广人员在该平台的每日预算配置
        $budget = AdsAdvertisingBudget::find()
            ->where([
                'promote_user_id' => $userId,
                'platform' => $platform,
                'entity_id' => UserService::getInst()->current_entity_id
            ])
            ->one();

        if (empty($budget)) {
            // 没有配置预算，不进行限制
            return;
        }

        $dailyBudget = (float)$budget->budget;
        if ($dailyBudget <= 0) {
            // 预算为0或负数，不进行限制
            return;
        }

        // 获取今日已充金额
        $todayTransferAmounts = AdsAdvertisingBudgetService::getTodayTransferAmountsByUserIds(
            [$userId],
            $platform
        );
        $todayUsedAmount = (float)($todayTransferAmounts[$userId] ?? 0);

        // 计算剩余可充值金额
        $remainingAmount = $dailyBudget - $todayUsedAmount;

        // 检查是否超出限额
        if ($amount > $remainingAmount) {
            $this->code = $this->error_code_budget_limit;

            // 构建详细的错误信息
            $accountInfo = implode('、', $accountIds);
            $errorMessage = "充值被拒绝：超出每日预算限额\n";
            $errorMessage .= "充值账户：{$accountInfo}\n";
            $errorMessage .= "平台：{$platform}\n";
            $errorMessage .= "本次充值金额：{$amount}元\n";
            $errorMessage .= "每日预算：{$dailyBudget}元\n";
            $errorMessage .= "今日已充金额：{$todayUsedAmount}元\n";
            $errorMessage .= "剩余可充值金额：{$remainingAmount}元";

            throw new Exception($errorMessage);
        }
    }

    /**
     * 获取充值限额错误码
     *
     * @return int
     */
    public function getErrorCodeBudgetLimit()
    {
        return $this->error_code_budget_limit;
    }

    /**
     * 检查账户状态
     *
     * @param array $targetAdvertiserIds 目标账户ID数组
     * @throws Exception
     */
    private function checkAccountStatus($targetAdvertiserIds)
    {
        if (empty($targetAdvertiserIds)) {
            return;
        }

        try {
            // 批量查询账户信息和投放人员状态
            $accountsInfo = AdsAccountSub::find()
                ->alias('aas')
                ->select([
                    'aas.sub_advertiser_id',
                    'aas.sub_advertiser_name',
                    'aas.status as account_status',
                    'aas.responsible_id',
                    'm.status as member_status',
                    'm.username as member_username',
                    'm.realname as member_realname'
                ])
                ->leftJoin(['m' => Member::tableName()], 'm.id = aas.responsible_id')
                ->where(['aas.sub_advertiser_id' => $targetAdvertiserIds])
                ->andWhere(['aas.entity_id' => UserService::getInst()->current_entity_id])
                ->asArray()
                ->all();

            $accountsMap = [];
            foreach ($accountsInfo as $account) {
                $accountsMap[$account['sub_advertiser_id']] = $account;
            }

            $errorAccounts = [];

            // 检查每个账户的状态
            foreach ($targetAdvertiserIds as $accountId) {
                $account = $accountsMap[$accountId] ?? null;

                if (empty($account)) {
                    // 账户不存在
                    $errorAccounts[] = $accountId . ' 账户不存在';
                    continue;
                }

                // 检查账户状态
                if ($account['account_status'] != AdsAccountSubStatusEnum::ENABLED) {
                    $statusText = AdsAccountSubStatusEnum::getValue($account['account_status']) ?? '未知状态';
                    $errorAccounts[] = $accountId . ' 账户状态为' . $statusText . '，非启用状态';
                    continue;
                }

                // 检查投放人员状态
                if (!empty($account['responsible_id'])) {
                    if (empty($account['member_status']) || $account['member_status'] != StatusEnum::ENABLED) {
                        $memberInfo = !empty($account['member_realname']) ?
                            "({$account['member_realname']})" :
                            (!empty($account['member_username']) ? "({$account['member_username']})" : '');
                        $errorAccounts[] = $accountId . ' 绑定的投放人员' . $memberInfo . '状态异常或已离职';
                    }
                }
            }

            // 如果有错误账户，抛出异常
            if (!empty($errorAccounts)) {
                $this->code = $this->error_code_account_status;
                $errorMessage = implode('；', $errorAccounts) . '，请前往账户管理确认账户状态后再进行充值';
                throw new Exception($errorMessage);
            }

        } catch (Exception $e) {
            // 如果是账户状态异常，直接重新抛出
            if ($this->code == $this->error_code_account_status) {
                throw $e;
            }

            // 其他异常记录日志但不阻止充值（保持向后兼容）
            Yii::error("账户状态检查异常: " . $e->getMessage(), 'account_status_check');
        }
    }

    /**
     * 获取账户状态错误码
     *
     * @return int
     */
    public function getErrorCodeAccountStatus()
    {
        return $this->error_code_account_status;
    }
}