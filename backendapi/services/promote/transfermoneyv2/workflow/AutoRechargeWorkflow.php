<?php

namespace backendapi\services\promote\transfermoneyv2\workflow;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\validator\TimeValidator;
use backendapi\services\promote\transfermoneyv2\validator\AccountValidator;
use backendapi\services\promote\transfermoneyv2\validator\AmountValidator;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use common\queues\TransferMoneyJobV2;
use Exception;
use Yii;

/**
 * 广告预算自动充值入队流程
 * 
 * 基于现有业务逻辑实现广告预算自动充值的完整工作流程
 * 包括时间限制检查、参数处理、账户验证、金额验证、定时充值判断等步骤
 */
class AutoRechargeWorkflow
{
    /**
     * @var TransferMoneyServiceV2 业务服务
     */
    private $service;

    /**
     * @var TransferCacheManager 缓存管理器
     */
    private $cacheManager;

    /**
     * @var array 工作流配置
     */
    private $config;

    /**
     * @var array 执行步骤记录
     */
    private $executionSteps = [];

    /**
     * 构造函数
     * 
     * @param TransferMoneyServiceV2 $service 业务服务
     * @param array $config 工作流配置
     */
    public function __construct(TransferMoneyServiceV2 $service, array $config = [])
    {
        $this->service = $service;
        $this->cacheManager = $service->getCacheManager();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * 执行广告预算自动充值入队流程
     * 
     * @param array $params 充值参数
     * @return array 执行结果
     * @throws Exception
     */
    public function execute(array $params): array
    {
        $this->executionSteps = [];
        
        try {
            // 步骤1：时间限制检查
            $this->executeStep('timeLimit', function() {
                $this->service->timeLimit();
                return '时间限制检查通过';
            });

            // 步骤2：参数处理
            $processedParams = $this->executeStep('dealParams', function() use ($params) {
                return $this->service->dealParams($params);
            });

            // 步骤3：账户验证
            $targetAdvertiserIds = $this->executeStep('verificationAccountWithPlatforms', function() use ($processedParams) {
                return $this->service->verificationAccountWithPlatforms($processedParams);
            });

            // 步骤4：金额验证
            $amount = $this->executeStep('verificationAmount', function() use ($processedParams) {
                return $this->service->verificationAmount($processedParams);
            });

            // 步骤5：定时充值判断
            $this->executeStep('isTimeRecharge', function() use ($processedParams) {
                $this->service->isTimeRecharge($processedParams);
                return '定时充值判断完成';
            });

            // 步骤6：构建队列数据
            $queueData = $this->executeStep('buildQueueData', function() use ($targetAdvertiserIds, $amount, $params) {
                return $this->buildQueueData($targetAdvertiserIds, $amount, $params);
            });

            // 步骤7：添加到队列
            $result = $this->executeStep('addToQueue', function() use ($queueData) {
                return $this->addToQueue($queueData);
            });

            return [
                'success' => true,
                'result' => $result,
                'code' => $this->service->getCode(),
                'execution_steps' => $this->executionSteps
            ];

        } catch (Exception $e) {
            $this->logError('工作流执行失败', $e, $params);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'code' => $this->service->getCode(),
                'execution_steps' => $this->executionSteps
            ];
        }
    }

    /**
     * 执行单个步骤
     * 
     * @param string $stepName 步骤名称
     * @param callable $callback 执行回调
     * @return mixed 执行结果
     */
    private function executeStep(string $stepName, callable $callback)
    {
        $startTime = microtime(true);
        
        try {
            $result = $callback();
            $duration = microtime(true) - $startTime;
            
            $this->executionSteps[] = [
                'step' => $stepName,
                'status' => 'success',
                'duration' => $duration,
                'result' => is_string($result) ? $result : 'completed',
                'timestamp' => time()
            ];
            
            Yii::info("工作流步骤执行成功: {$stepName}", 'auto_recharge_workflow');
            
            return $result;
            
        } catch (Exception $e) {
            $duration = microtime(true) - $startTime;
            
            $this->executionSteps[] = [
                'step' => $stepName,
                'status' => 'failed',
                'duration' => $duration,
                'error' => $e->getMessage(),
                'timestamp' => time()
            ];
            
            Yii::error("工作流步骤执行失败: {$stepName} - {$e->getMessage()}", 'auto_recharge_workflow');
            
            throw $e;
        }
    }

    /**
     * 构建队列数据
     * 
     * @param array $targetAdvertiserIds 目标账户ID列表
     * @param float $amount 充值金额
     * @param array $params 原始参数
     * @return array 队列数据
     */
    private function buildQueueData(array $targetAdvertiserIds, float $amount, array $params): array
    {
        $data = [
            'target_advertiser_ids' => $targetAdvertiserIds,
            'amount' => $amount,
            'user_name' => $params['user_name'] ?? '系统自动',
            'user_id' => $params['user_id'] ?? 0,
            'create_time' => time()
        ];

        // 定时充值处理
        if ($this->service->isTimeRechargeEnabled()) {
            $data['isTimeRecharge'] = true;
            $data['execute_time'] = $this->service->getTimeRecharge();
        } else {
            $data['isTimeRecharge'] = false;
            $data['execute_time'] = time();
        }

        return $data;
    }

    /**
     * 添加到队列
     * 
     * @param array $data 队列数据
     * @return string|array 添加结果
     */
    private function addToQueue(array $data)
    {
        TransferMoneyJobV2::addJob($data);

        if ($data['isTimeRecharge']) {
            return '定时充值操作成功';
        } else {
            return ['200' => ['code' => 200, 'msg' => '充值成功']];
        }
    }

    /**
     * 批量充值入队流程
     * 
     * @param array $batchParams 批量充值参数
     * @return array 批量执行结果
     */
    public function executeBatch(array $batchParams): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($batchParams as $index => $params) {
            try {
                $result = $this->execute($params);
                $results[$index] = $result;
                
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
                
            } catch (Exception $e) {
                $results[$index] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'code' => 422
                ];
                $failureCount++;
            }
        }

        return [
            'batch_results' => $results,
            'summary' => [
                'total' => count($batchParams),
                'success' => $successCount,
                'failure' => $failureCount,
                'success_rate' => $successCount / count($batchParams) * 100
            ]
        ];
    }

    /**
     * 验证充值前置条件
     * 
     * @param array $params 充值参数
     * @return array 验证结果
     */
    public function validatePreconditions(array $params): array
    {
        $validations = [];

        try {
            // 时间验证
            $timeValidator = TimeValidator::createFromConfig();
            $timeValidator->validate($params);
            $validations['time'] = ['status' => 'passed', 'message' => '时间验证通过'];
        } catch (Exception $e) {
            $validations['time'] = ['status' => 'failed', 'message' => $e->getMessage()];
        }

        try {
            // 账户验证
            $accountValidator = new AccountValidator();
            $accountValidator->validate($params);
            $validations['account'] = ['status' => 'passed', 'message' => '账户验证通过'];
        } catch (Exception $e) {
            $validations['account'] = ['status' => 'failed', 'message' => $e->getMessage()];
        }

        try {
            // 金额验证
            $amountValidator = new AmountValidator();
            $amountValidator->validate($params);
            $validations['amount'] = ['status' => 'passed', 'message' => '金额验证通过'];
        } catch (Exception $e) {
            $validations['amount'] = ['status' => 'failed', 'message' => $e->getMessage()];
        }

        $allPassed = array_reduce($validations, function($carry, $validation) {
            return $carry && $validation['status'] === 'passed';
        }, true);

        return [
            'all_passed' => $allPassed,
            'validations' => $validations
        ];
    }

    /**
     * 获取工作流状态
     * 
     * @return array 工作流状态信息
     */
    public function getWorkflowStatus(): array
    {
        return [
            'service_code' => $this->service->getCode(),
            'is_time_recharge' => $this->service->isTimeRechargeEnabled(),
            'execution_steps' => $this->executionSteps,
            'config' => $this->config
        ];
    }

    /**
     * 重置工作流状态
     */
    public function reset(): void
    {
        $this->executionSteps = [];
        $this->service->initialize();
    }

    /**
     * 获取默认配置
     * 
     * @return array 默认配置
     */
    private function getDefaultConfig(): array
    {
        return [
            'enable_logging' => true,
            'enable_validation' => true,
            'enable_cache' => true,
            'timeout' => 30,
            'retry_times' => 3,
            'batch_size' => 50
        ];
    }

    /**
     * 记录错误日志
     * 
     * @param string $message 错误消息
     * @param Exception $exception 异常对象
     * @param array $context 上下文信息
     */
    private function logError(string $message, Exception $exception, array $context = []): void
    {
        if (!$this->config['enable_logging']) {
            return;
        }

        $logData = [
            'message' => $message,
            'exception' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'context' => $context,
            'execution_steps' => $this->executionSteps
        ];

        Yii::error($logData, 'auto_recharge_workflow');
    }

    /**
     * 获取执行统计信息
     * 
     * @return array 统计信息
     */
    public function getExecutionStats(): array
    {
        if (empty($this->executionSteps)) {
            return ['total_steps' => 0, 'total_duration' => 0];
        }

        $totalDuration = array_sum(array_column($this->executionSteps, 'duration'));
        $successSteps = array_filter($this->executionSteps, function($step) {
            return $step['status'] === 'success';
        });
        $failedSteps = array_filter($this->executionSteps, function($step) {
            return $step['status'] === 'failed';
        });

        return [
            'total_steps' => count($this->executionSteps),
            'success_steps' => count($successSteps),
            'failed_steps' => count($failedSteps),
            'total_duration' => $totalDuration,
            'average_duration' => $totalDuration / count($this->executionSteps),
            'success_rate' => count($successSteps) / count($this->executionSteps) * 100
        ];
    }

    /**
     * 创建工作流实例
     * 
     * @param array $config 配置参数
     * @return AutoRechargeWorkflow
     */
    public static function create(array $config = []): AutoRechargeWorkflow
    {
        $service = new TransferMoneyServiceV2();
        return new self($service, $config);
    }

    /**
     * 创建带有自定义服务的工作流实例
     * 
     * @param TransferMoneyServiceV2 $service 自定义服务
     * @param array $config 配置参数
     * @return AutoRechargeWorkflow
     */
    public static function createWithService(TransferMoneyServiceV2 $service, array $config = []): AutoRechargeWorkflow
    {
        return new self($service, $config);
    }
}